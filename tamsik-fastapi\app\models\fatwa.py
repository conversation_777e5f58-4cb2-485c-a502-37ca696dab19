"""
نموذج الفتوى - Tamsik FastAPI
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.database import Base


class FatwaStatus(str, enum.Enum):
    """حالات الفتوى"""
    PENDING = "pending"
    ANSWERED = "answered"
    PUBLISHED = "published"
    ARCHIVED = "archived"


class Fatwa(Base):
    """نموذج الفتوى"""
    
    __tablename__ = "fatwas"
    
    # الحقول الأساسية
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=True)
    
    # السائل
    questioner_name = Column(String(100), nullable=True)
    questioner_email = Column(String(100), nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # إذا كان مسجل
    
    # العالم والتصنيف
    scholar_id = Column(Integer, ForeignKey("scholars.id"), nullable=True)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    
    # الحالة والإعدادات
    status = Column(Enum(FatwaStatus), default=FatwaStatus.PENDING, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    is_public = Column(Boolean, default=True, nullable=False)
    
    # الإحصائيات
    views_count = Column(Integer, default=0, nullable=False)
    likes_count = Column(Integer, default=0, nullable=False)
    
    # البيانات الإضافية
    tags = Column(JSON, nullable=True)  # الكلمات المفتاحية
    references = Column(JSON, nullable=True)  # المراجع والأدلة
    
    # معلومات SEO
    meta_title = Column(String(255), nullable=True)
    meta_description = Column(Text, nullable=True)
    slug = Column(String(255), unique=True, index=True, nullable=True)
    
    # التواريخ
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    answered_at = Column(DateTime(timezone=True), nullable=True)
    published_at = Column(DateTime(timezone=True), nullable=True)
    
    # العلاقات
    questioner = relationship("User", back_populates="fatwas")
    scholar = relationship("Scholar", back_populates="fatwas")
    category = relationship("Category", back_populates="fatwas")
    
    def __repr__(self):
        return f"<Fatwa(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    @property
    def is_answered(self) -> bool:
        """التحقق من الإجابة على الفتوى"""
        return self.status in [FatwaStatus.ANSWERED, FatwaStatus.PUBLISHED]
    
    @property
    def is_published(self) -> bool:
        """التحقق من نشر الفتوى"""
        return self.status == FatwaStatus.PUBLISHED
    
    def increment_views(self):
        """زيادة عدد المشاهدات"""
        self.views_count += 1
    
    def increment_likes(self):
        """زيادة عدد الإعجابات"""
        self.likes_count += 1
