"""
نموذج الخطبة - Tamsik FastAPI
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.database import Base


class SermonStatus(str, enum.Enum):
    """حالات الخطبة"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    PENDING_REVIEW = "pending_review"


class Sermon(Base):
    """نموذج الخطبة"""
    
    __tablename__ = "sermons"
    
    # الحقول الأساسية
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    content = Column(Text, nullable=False)
    excerpt = Column(Text, nullable=True)
    
    # المؤلف والتصنيف
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    
    # الحالة والإعدادات
    status = Column(Enum(SermonStatus), default=SermonStatus.DRAFT, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    is_friday_sermon = Column(Boolean, default=False, nullable=False)
    
    # الإحصائيات
    views_count = Column(Integer, default=0, nullable=False)
    downloads_count = Column(Integer, default=0, nullable=False)
    likes_count = Column(Integer, default=0, nullable=False)
    
    # البيانات الإضافية
    tags = Column(JSON, nullable=True)  # قائمة الكلمات المفتاحية
    attachments = Column(JSON, nullable=True)  # المرفقات
    
    # المحتوى المنظم (للخطب المتقدمة)
    structured_content = Column(JSON, nullable=True)
    
    # معلومات SEO
    meta_title = Column(String(255), nullable=True)
    meta_description = Column(Text, nullable=True)
    slug = Column(String(255), unique=True, index=True, nullable=True)
    
    # التواريخ
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    published_at = Column(DateTime(timezone=True), nullable=True)
    
    # العلاقات
    author = relationship("User", back_populates="sermons")
    category = relationship("Category", back_populates="sermons")
    
    def __repr__(self):
        return f"<Sermon(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    @property
    def is_published(self) -> bool:
        """التحقق من نشر الخطبة"""
        return self.status == SermonStatus.PUBLISHED
    
    @property
    def word_count(self) -> int:
        """عدد الكلمات في الخطبة"""
        if not self.content:
            return 0
        return len(self.content.split())
    
    @property
    def reading_time(self) -> int:
        """وقت القراءة المقدر بالدقائق (متوسط 200 كلمة/دقيقة)"""
        return max(1, self.word_count // 200)
    
    def increment_views(self):
        """زيادة عدد المشاهدات"""
        self.views_count += 1
    
    def increment_downloads(self):
        """زيادة عدد التحميلات"""
        self.downloads_count += 1
    
    def increment_likes(self):
        """زيادة عدد الإعجابات"""
        self.likes_count += 1
