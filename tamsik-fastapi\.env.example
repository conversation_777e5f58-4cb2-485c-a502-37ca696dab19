# Database Configuration
DATABASE_URL=mysql+aiomysql://username:password@localhost:3306/tamsik_db
DATABASE_URL_SYNC=mysql+pymysql://username:password@localhost:3306/tamsik_db

# Alternative PostgreSQL (recommended for production)
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/tamsik_db

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-here-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Application Settings
APP_NAME=Tamsik Islamic Platform
APP_VERSION=2.0.0
DEBUG=True
ENVIRONMENT=development

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8000", "https://yourdomain.com"]

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# Email Configuration
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM=<EMAIL>
MAIL_PORT=587
MAIL_SERVER=smtp.gmail.com
MAIL_FROM_NAME=Tamsik Platform

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads/
ALLOWED_EXTENSIONS=["jpg", "jpeg", "png", "pdf", "doc", "docx"]

# Pagination Settings
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/tamsik.log

# Security
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# Arabic Text Processing
DEFAULT_LANGUAGE=ar
SUPPORT_RTL=True
