# 🕌 Tamsik FastAPI - منصة تمسيك الإسلامية

منصة إسلامية شاملة مبنية بـ FastAPI لإدارة الخطب والفتاوى والمحاضرات والمحتوى الإسلامي.

## ✨ المميزات

- 🚀 **أداء فائق** - مبني بـ FastAPI مع دعم async/await
- 🔐 **نظام أمان متقدم** - JWT authentication مع role-based access control
- 📚 **إدارة المحتوى** - خطب، فتاوى، محاضرات، علماء
- 🔍 **بحث متقدم** - بحث ذكي في النصوص العربية
- 📱 **API موثق** - Swagger UI تلقائي
- 🌐 **دعم العربية** - معالجة متقدمة للنصوص العربية
- 📊 **إحصائيات شاملة** - تتبع المشاهدات والتفاعل
- 🔄 **نظام الاقتراحات** - اقتراحات ذكية للآيات والأحاديث

## 🛠️ التقنيات المستخدمة

- **FastAPI** - إطار عمل Python حديث وسريع
- **SQLAlchemy** - ORM متقدم مع دعم async
- **Pydantic** - تحقق من البيانات والتسلسل
- **JWT** - نظام مصادقة آمن
- **MySQL/PostgreSQL** - قاعدة بيانات قوية
- **Redis** - تخزين مؤقت وجلسات
- **Alembic** - إدارة هجرة قاعدة البيانات

## 🚀 التثبيت والتشغيل

### المتطلبات

- Python 3.8+
- MySQL أو PostgreSQL
- Redis (اختياري)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/tamsik-fastapi.git
cd tamsik-fastapi
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# قم بتحرير .env وإضافة إعداداتك
```

5. **إعداد قاعدة البيانات**
```bash
# إنشاء قاعدة البيانات
alembic upgrade head
```

6. **تشغيل التطبيق**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 📖 استخدام API

### الوصول للوثائق

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### أمثلة على الاستخدام

#### تسجيل مستخدم جديد
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "ahmed_user",
    "email": "<EMAIL>",
    "password": "securepassword123",
    "confirm_password": "securepassword123",
    "full_name": "أحمد محمد"
  }'
```

#### تسجيل الدخول
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

#### الحصول على الخطب
```bash
curl -X GET "http://localhost:8000/api/v1/sermons?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🏗️ هيكل المشروع

```
tamsik-fastapi/
├── app/
│   ├── api/                 # API routes
│   │   └── v1/
│   │       ├── auth.py      # نظام المصادقة
│   │       ├── users.py     # إدارة المستخدمين
│   │       ├── sermons.py   # إدارة الخطب
│   │       └── ...
│   ├── core/                # المكونات الأساسية
│   │   ├── security.py      # نظام الأمان
│   │   ├── exceptions.py    # معالجة الأخطاء
│   │   └── middleware.py    # Middleware مخصص
│   ├── crud/                # عمليات قاعدة البيانات
│   ├── models/              # نماذج SQLAlchemy
│   ├── schemas/             # Pydantic schemas
│   ├── services/            # منطق الأعمال
│   ├── utils/               # أدوات مساعدة
│   ├── config.py            # إعدادات التطبيق
│   ├── database.py          # إعداد قاعدة البيانات
│   └── main.py              # نقطة دخول التطبيق
├── alembic/                 # هجرة قاعدة البيانات
├── tests/                   # الاختبارات
├── requirements.txt         # المتطلبات
└── README.md
```

## 🔐 نظام الأدوار والصلاحيات

- **Admin**: صلاحيات كاملة لإدارة النظام
- **Scholar**: إدارة الفتاوى والمحتوى العلمي
- **Member**: إنشاء ومشاركة المحتوى
- **User**: قراءة المحتوى والتفاعل

## 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
pytest

# تشغيل اختبارات محددة
pytest tests/test_auth.py

# تشغيل مع تقرير التغطية
pytest --cov=app tests/
```

## 📊 المراقبة والسجلات

- السجلات تُحفظ في مجلد `logs/`
- مراقبة الأداء عبر middleware مخصص
- إحصائيات مفصلة للاستخدام

## 🔄 النشر

### Docker

```bash
# بناء الصورة
docker build -t tamsik-fastapi .

# تشغيل الحاوية
docker run -p 8000:8000 tamsik-fastapi
```

### Docker Compose

```bash
docker-compose up -d
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://tamsik.com
- **GitHub**: https://github.com/your-username/tamsik-fastapi

## 🙏 شكر وتقدير

- فريق FastAPI لإطار العمل الرائع
- مجتمع Python العربي
- جميع المساهمين في المشروع

---

**تم تطوير هذا المشروع بـ ❤️ لخدمة المجتمع الإسلامي**
