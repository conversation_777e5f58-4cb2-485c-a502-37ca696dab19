"""
Middleware مخصص - Tamsik FastAPI
"""
import time
import logging
from typing import Callable
from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import uuid

from app.config import settings


# إعداد السجلات
logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware لتسجيل الطلبات"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # إنشاء معرف فريد للطلب
        request_id = str(uuid.uuid4())
        
        # تسجيل بداية الطلب
        start_time = time.time()
        
        # إضافة معرف الطلب إلى headers
        request.state.request_id = request_id
        
        # معلومات الطلب
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        logger.info(
            f"Request started - ID: {request_id} | "
            f"Method: {request.method} | "
            f"URL: {request.url} | "
            f"IP: {client_ip} | "
            f"User-Agent: {user_agent}"
        )
        
        try:
            # تنفيذ الطلب
            response = await call_next(request)
            
            # حساب وقت التنفيذ
            process_time = time.time() - start_time
            
            # إضافة headers للاستجابة
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            # تسجيل انتهاء الطلب
            logger.info(
                f"Request completed - ID: {request_id} | "
                f"Status: {response.status_code} | "
                f"Time: {process_time:.4f}s"
            )
            
            return response
            
        except Exception as e:
            # تسجيل الأخطاء
            process_time = time.time() - start_time
            logger.error(
                f"Request failed - ID: {request_id} | "
                f"Error: {str(e)} | "
                f"Time: {process_time:.4f}s",
                exc_info=e
            )
            raise


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware للحد من معدل الطلبات"""
    
    def __init__(self, app, calls: int = 60, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # الحصول على IP العميل
        client_ip = request.client.host if request.client else "unknown"
        
        # التحقق من معدل الطلبات
        current_time = time.time()
        
        if client_ip in self.clients:
            client_data = self.clients[client_ip]
            
            # إزالة الطلبات القديمة
            client_data["requests"] = [
                req_time for req_time in client_data["requests"]
                if current_time - req_time < self.period
            ]
            
            # التحقق من تجاوز الحد
            if len(client_data["requests"]) >= self.calls:
                return JSONResponse(
                    status_code=429,
                    content={
                        "success": False,
                        "message": "تم تجاوز الحد المسموح من الطلبات",
                        "retry_after": self.period
                    }
                )
            
            # إضافة الطلب الحالي
            client_data["requests"].append(current_time)
        else:
            # عميل جديد
            self.clients[client_ip] = {
                "requests": [current_time]
            }
        
        # تنفيذ الطلب
        response = await call_next(request)
        
        # إضافة headers معلومات Rate Limit
        remaining = self.calls - len(self.clients[client_ip]["requests"])
        response.headers["X-RateLimit-Limit"] = str(self.calls)
        response.headers["X-RateLimit-Remaining"] = str(max(0, remaining))
        response.headers["X-RateLimit-Reset"] = str(int(current_time + self.period))
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware لإضافة headers الأمان"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # إضافة headers الأمان
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        if not settings.debug:
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response


class CacheControlMiddleware(BaseHTTPMiddleware):
    """Middleware للتحكم في التخزين المؤقت"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # إعدادات التخزين المؤقت حسب نوع المحتوى
        if request.url.path.startswith("/api/"):
            # API responses - no cache
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        elif request.url.path.startswith("/static/"):
            # Static files - cache for 1 year
            response.headers["Cache-Control"] = "public, max-age=31536000"
        elif request.url.path.startswith("/uploads/"):
            # Uploaded files - cache for 1 day
            response.headers["Cache-Control"] = "public, max-age=86400"
        
        return response


def setup_middleware(app: FastAPI):
    """إعداد جميع middleware"""
    
    # إضافة middleware للتسجيل
    app.add_middleware(RequestLoggingMiddleware)
    
    # إضافة middleware للحد من معدل الطلبات
    if not settings.debug:
        app.add_middleware(
            RateLimitMiddleware,
            calls=settings.rate_limit_per_minute,
            period=60
        )
    
    # إضافة middleware للأمان
    app.add_middleware(SecurityHeadersMiddleware)
    
    # إضافة middleware للتخزين المؤقت
    app.add_middleware(CacheControlMiddleware)
    
    logger.info("✅ تم إعداد جميع middleware بنجاح")


# تصدير المكونات المهمة
__all__ = [
    "RequestLoggingMiddleware",
    "RateLimitMiddleware", 
    "SecurityHeadersMiddleware",
    "CacheControlMiddleware",
    "setup_middleware"
]
