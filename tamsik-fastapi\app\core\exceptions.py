"""
معالجة الأخطاء المخصصة - Tamsik FastAPI
"""
from typing import Union
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging

from app.config import settings


# إعداد السجلات
logger = logging.getLogger(__name__)


class TamsikException(Exception):
    """استثناء مخصص لتطبيق تمسيك"""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        details: Union[str, dict] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details
        super().__init__(self.message)


class ValidationException(TamsikException):
    """استثناء التحقق من صحة البيانات"""
    
    def __init__(self, message: str, details: Union[str, dict] = None):
        super().__init__(message, 400, details)


class AuthenticationException(TamsikException):
    """استثناء المصادقة"""
    
    def __init__(self, message: str = "فشل في المصادقة"):
        super().__init__(message, 401)


class AuthorizationException(TamsikException):
    """استثناء التخويل"""
    
    def __init__(self, message: str = "ليس لديك صلاحية للوصول"):
        super().__init__(message, 403)


class NotFoundException(TamsikException):
    """استثناء عدم وجود المورد"""
    
    def __init__(self, message: str = "المورد غير موجود"):
        super().__init__(message, 404)


class ConflictException(TamsikException):
    """استثناء التضارب"""
    
    def __init__(self, message: str = "يوجد تضارب في البيانات"):
        super().__init__(message, 409)


class DatabaseException(TamsikException):
    """استثناء قاعدة البيانات"""
    
    def __init__(self, message: str = "خطأ في قاعدة البيانات"):
        super().__init__(message, 500)


def create_error_response(
    message: str,
    status_code: int = 500,
    details: Union[str, dict] = None,
    error_code: str = None
) -> JSONResponse:
    """إنشاء استجابة خطأ موحدة"""
    
    content = {
        "success": False,
        "message": message,
        "status_code": status_code
    }
    
    if details:
        content["details"] = details
    
    if error_code:
        content["error_code"] = error_code
    
    if settings.debug and hasattr(details, '__traceback__'):
        import traceback
        content["traceback"] = traceback.format_exc()
    
    return JSONResponse(
        status_code=status_code,
        content=content
    )


def setup_exception_handlers(app: FastAPI):
    """إعداد معالجات الأخطاء"""
    
    @app.exception_handler(TamsikException)
    async def tamsik_exception_handler(request: Request, exc: TamsikException):
        """معالج الأخطاء المخصصة لتمسيك"""
        logger.error(f"TamsikException: {exc.message}", exc_info=exc)
        
        return create_error_response(
            message=exc.message,
            status_code=exc.status_code,
            details=exc.details
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """معالج أخطاء HTTP"""
        logger.warning(f"HTTPException: {exc.detail}")
        
        return create_error_response(
            message=exc.detail,
            status_code=exc.status_code
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def starlette_exception_handler(request: Request, exc: StarletteHTTPException):
        """معالج أخطاء Starlette HTTP"""
        logger.warning(f"StarletteHTTPException: {exc.detail}")
        
        return create_error_response(
            message=exc.detail,
            status_code=exc.status_code
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """معالج أخطاء التحقق من صحة البيانات"""
        logger.warning(f"ValidationError: {exc.errors()}")
        
        # تحويل أخطاء التحقق إلى رسائل عربية
        errors = []
        for error in exc.errors():
            field = " -> ".join(str(loc) for loc in error["loc"][1:])  # تجاهل 'body'
            message = error["msg"]
            
            # ترجمة بعض الرسائل الشائعة
            if "field required" in message:
                message = f"الحقل '{field}' مطلوب"
            elif "ensure this value" in message:
                message = f"قيمة الحقل '{field}' غير صحيحة"
            elif "string too short" in message:
                message = f"الحقل '{field}' قصير جداً"
            elif "string too long" in message:
                message = f"الحقل '{field}' طويل جداً"
            elif "invalid email" in message:
                message = f"البريد الإلكتروني في الحقل '{field}' غير صحيح"
            
            errors.append({
                "field": field,
                "message": message,
                "type": error["type"]
            })
        
        return create_error_response(
            message="خطأ في التحقق من صحة البيانات",
            status_code=422,
            details={"errors": errors}
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """معالج الأخطاء العام"""
        logger.error(f"Unhandled exception: {str(exc)}", exc_info=exc)
        
        if settings.debug:
            import traceback
            return create_error_response(
                message="حدث خطأ داخلي في الخادم",
                status_code=500,
                details={
                    "error": str(exc),
                    "traceback": traceback.format_exc()
                }
            )
        else:
            return create_error_response(
                message="حدث خطأ داخلي في الخادم",
                status_code=500
            )


# تصدير المكونات المهمة
__all__ = [
    "TamsikException",
    "ValidationException", 
    "AuthenticationException",
    "AuthorizationException",
    "NotFoundException",
    "ConflictException",
    "DatabaseException",
    "create_error_response",
    "setup_exception_handlers"
]
