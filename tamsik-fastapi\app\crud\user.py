"""
User CRUD operations - Tamsik FastAPI
"""
from typing import Any, Dict, Optional, Union
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta
import secrets

from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import User<PERSON>reate, UserUpdate
from app.core.security import security_manager


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    """CRUD operations for User"""
    
    async def get_by_email(self, db: AsyncSession, *, email: str) -> Optional[User]:
        """Get user by email"""
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()
    
    async def get_by_username(self, db: AsyncSession, *, username: str) -> Optional[User]:
        """Get user by username"""
        result = await db.execute(select(User).where(User.username == username))
        return result.scalar_one_or_none()
    
    async def create(self, db: AsyncSession, *, obj_in: UserCreate) -> User:
        """Create new user with hashed password"""
        # Hash the password
        hashed_password = security_manager.get_password_hash(obj_in.password)
        
        # Create user data
        user_data = {
            "username": obj_in.username,
            "email": obj_in.email,
            "hashed_password": hashed_password,
            "full_name": obj_in.full_name,
            "role": obj_in.role,
            "phone": obj_in.phone,
            "location": obj_in.location,
            "bio": obj_in.bio,
            "verification_token": secrets.token_urlsafe(32)
        }
        
        db_obj = User(**user_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def authenticate(
        self, 
        db: AsyncSession, 
        *, 
        email: str, 
        password: str
    ) -> Optional[User]:
        """Authenticate user with email and password"""
        user = await self.get_by_email(db, email=email)
        if not user:
            return None
        
        if not security_manager.verify_password(password, user.hashed_password):
            return None
        
        return user
    
    async def update_password(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        new_password: str
    ) -> User:
        """Update user password"""
        hashed_password = security_manager.get_password_hash(new_password)
        
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(
                hashed_password=hashed_password,
                reset_password_token=None,
                reset_password_expires=None,
                updated_at=datetime.utcnow()
            )
        )
        await db.commit()
        
        return await self.get(db, id=user_id)
    
    async def update_last_login(
        self,
        db: AsyncSession,
        *,
        user_id: int
    ) -> User:
        """Update user's last login timestamp"""
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(last_login=datetime.utcnow())
        )
        await db.commit()
        
        return await self.get(db, id=user_id)
    
    async def create_reset_token(
        self,
        db: AsyncSession,
        *,
        user_id: int
    ) -> str:
        """Create password reset token for user"""
        reset_token = secrets.token_urlsafe(32)
        expires = datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour
        
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(
                reset_password_token=reset_token,
                reset_password_expires=expires,
                updated_at=datetime.utcnow()
            )
        )
        await db.commit()
        
        return reset_token
    
    async def verify_reset_token(
        self,
        db: AsyncSession,
        *,
        token: str
    ) -> Optional[User]:
        """Verify password reset token"""
        result = await db.execute(
            select(User).where(
                User.reset_password_token == token,
                User.reset_password_expires > datetime.utcnow()
            )
        )
        return result.scalar_one_or_none()
    
    async def create_verification_token(
        self,
        db: AsyncSession,
        *,
        user_id: int
    ) -> str:
        """Create email verification token for user"""
        verification_token = secrets.token_urlsafe(32)
        
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(
                verification_token=verification_token,
                updated_at=datetime.utcnow()
            )
        )
        await db.commit()
        
        return verification_token
    
    async def verify_email_token(
        self,
        db: AsyncSession,
        *,
        token: str
    ) -> Optional[User]:
        """Verify email verification token"""
        result = await db.execute(
            select(User).where(User.verification_token == token)
        )
        user = result.scalar_one_or_none()
        
        if user:
            # Mark user as verified and clear token
            await db.execute(
                update(User)
                .where(User.id == user.id)
                .values(
                    is_verified=True,
                    verification_token=None,
                    updated_at=datetime.utcnow()
                )
            )
            await db.commit()
            await db.refresh(user)
        
        return user
    
    async def activate_user(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        is_active: bool = True
    ) -> User:
        """Activate or deactivate user"""
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(
                is_active=is_active,
                updated_at=datetime.utcnow()
            )
        )
        await db.commit()
        
        return await self.get(db, id=user_id)
    
    async def change_role(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        new_role: str
    ) -> User:
        """Change user role"""
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(
                role=new_role,
                updated_at=datetime.utcnow()
            )
        )
        await db.commit()
        
        return await self.get(db, id=user_id)
    
    async def get_user_stats(
        self,
        db: AsyncSession,
        *,
        user_id: int
    ) -> Dict[str, Any]:
        """Get user statistics"""
        user = await self.get(db, id=user_id)
        if not user:
            return {}
        
        # Count user's content (will be implemented when sermon/fatwa models are ready)
        stats = {
            "sermons_count": 0,  # len(user.sermons) if user.sermons else 0
            "fatwas_count": 0,   # len(user.fatwas) if user.fatwas else 0
            "total_views": 0,
            "total_likes": 0,
            "member_since": user.created_at,
            "last_login": user.last_login
        }
        
        return stats


# Create instance
user_crud = CRUDUser(User)
