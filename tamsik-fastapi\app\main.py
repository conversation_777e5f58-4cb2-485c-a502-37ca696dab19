"""
التطبيق الرئيسي - Tamsik FastAPI
منصة إسلامية شاملة للخطب والفتاوى والمحاضرات
"""
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import time
import os
from contextlib import asynccontextmanager

from app.config import settings
from app.database import init_db, close_db
from app.core.exceptions import setup_exception_handlers
from app.core.middleware import setup_middleware
from app.api.v1 import api_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    إدارة دورة حياة التطبيق
    """
    # بدء التطبيق
    print("🚀 بدء تشغيل منصة تمسيك...")
    
    # تهيئة قاعدة البيانات
    await init_db()
    print("✅ تم تهيئة قاعدة البيانات")
    
    # إنشاء مجلدات الرفع إذا لم تكن موجودة
    os.makedirs(settings.upload_dir, exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    yield
    
    # إغلاق التطبيق
    print("🔄 إغلاق منصة تمسيك...")
    await close_db()
    print("✅ تم إغلاق اتصالات قاعدة البيانات")


# إنشاء تطبيق FastAPI
app = FastAPI(
    title=settings.app_name,
    description="منصة إسلامية شاملة للخطب والفتاوى والمحاضرات والمحتوى الإسلامي",
    version=settings.app_version,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan,
    openapi_tags=[
        {
            "name": "auth",
            "description": "نظام المصادقة وتسجيل الدخول"
        },
        {
            "name": "users", 
            "description": "إدارة المستخدمين"
        },
        {
            "name": "sermons",
            "description": "إدارة الخطب"
        },
        {
            "name": "scholars",
            "description": "إدارة العلماء"
        },
        {
            "name": "fatwas",
            "description": "إدارة الفتاوى"
        },
        {
            "name": "lectures",
            "description": "إدارة المحاضرات"
        },
        {
            "name": "suggestions",
            "description": "نظام الاقتراحات"
        }
    ]
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد TrustedHost (للأمان)
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"]
    )

# إعداد middleware مخصص
setup_middleware(app)

# إعداد معالجات الأخطاء
setup_exception_handlers(app)

# إضافة الملفات الثابتة
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/uploads", StaticFiles(directory=settings.upload_dir), name="uploads")

# إضافة مسارات API
app.include_router(api_router, prefix="/api/v1")


# مسار الصفحة الرئيسية
@app.get("/")
async def root():
    """الصفحة الرئيسية للAPI"""
    return {
        "message": "مرحباً بكم في منصة تمسيك الإسلامية",
        "version": settings.app_version,
        "docs": "/docs" if settings.debug else "غير متاح في الإنتاج",
        "status": "يعمل بنجاح",
        "features": [
            "إدارة الخطب والمحاضرات",
            "نظام الفتاوى",
            "قاعدة بيانات العلماء",
            "نظام الاقتراحات الذكي",
            "البحث المتقدم في المحتوى العربي"
        ]
    }


# مسار فحص الصحة
@app.get("/health")
async def health_check():
    """فحص صحة التطبيق"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.app_version,
        "environment": settings.environment
    }


# مسار معلومات التطبيق
@app.get("/info")
async def app_info():
    """معلومات التطبيق"""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment,
        "debug": settings.debug,
        "language": settings.default_language,
        "rtl_support": settings.support_rtl
    }


# معالج الأخطاء العام
@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """معالج الأخطاء العام"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "حدث خطأ داخلي في الخادم",
            "error": str(exc) if settings.debug else "خطأ داخلي"
        }
    )


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
