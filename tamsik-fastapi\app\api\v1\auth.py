"""
Authentication API Routes - Tamsik FastAPI
"""
from datetime import timed<PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.core.security import security_manager, get_current_user, get_current_active_user
from app.core.exceptions import AuthenticationException, ValidationException
from app.schemas.user import (
    User<PERSON>reate, UserLogin, UserResponse, 
    ForgotPasswordRequest, ResetPasswordRequest,
    EmailVerificationRequest
)
from app.schemas.common import TokenResponse, MessageResponse, BaseResponse
from app.crud.user import user_crud
from app.models.user import User
from app.config import settings

router = APIRouter()


@router.post("/register", response_model=BaseResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    تسجيل مستخدم جديد
    """
    # التحقق من عدم وجود المستخدم مسبقاً
    existing_user = await user_crud.get_by_email(db, email=user_data.email)
    if existing_user:
        raise ValidationException("البريد الإلكتروني مستخدم مسبقاً")
    
    existing_username = await user_crud.get_by_username(db, username=user_data.username)
    if existing_username:
        raise ValidationException("اسم المستخدم مستخدم مسبقاً")
    
    # إنشاء المستخدم الجديد
    user = await user_crud.create(db, obj_in=user_data)
    
    # إنشاء tokens
    access_token = security_manager.create_access_token(subject=user.id)
    refresh_token = security_manager.create_refresh_token(subject=user.id)
    
    return BaseResponse(
        message="تم إنشاء الحساب بنجاح",
        data={
            "user": UserResponse.from_orm(user),
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }
    )


@router.post("/login", response_model=BaseResponse)
async def login(
    user_credentials: UserLogin,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    تسجيل الدخول
    """
    # البحث عن المستخدم
    user = await user_crud.authenticate(
        db, 
        email=user_credentials.email, 
        password=user_credentials.password
    )
    
    if not user:
        raise AuthenticationException("البريد الإلكتروني أو كلمة المرور غير صحيحة")
    
    if not user.is_active:
        raise AuthenticationException("الحساب غير نشط")
    
    # إنشاء tokens
    access_token_expires = timedelta(minutes=settings.jwt_access_token_expire_minutes)
    if user_credentials.remember_me:
        access_token_expires = timedelta(days=30)
    
    access_token = security_manager.create_access_token(
        subject=user.id, 
        expires_delta=access_token_expires
    )
    refresh_token = security_manager.create_refresh_token(subject=user.id)
    
    # تحديث آخر تسجيل دخول
    await user_crud.update_last_login(db, user_id=user.id)
    
    return BaseResponse(
        message="تم تسجيل الدخول بنجاح",
        data={
            "user": UserResponse.from_orm(user),
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": int(access_token_expires.total_seconds())
        }
    )


@router.post("/login/oauth", response_model=TokenResponse)
async def login_oauth(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    تسجيل الدخول باستخدام OAuth2 (للتوافق مع Swagger UI)
    """
    user = await user_crud.authenticate(
        db, 
        email=form_data.username,  # OAuth2 يستخدم username للبريد الإلكتروني
        password=form_data.password
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="البريد الإلكتروني أو كلمة المرور غير صحيحة",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="الحساب غير نشط"
        )
    
    access_token_expires = timedelta(minutes=settings.jwt_access_token_expire_minutes)
    access_token = security_manager.create_access_token(
        subject=user.id, 
        expires_delta=access_token_expires
    )
    
    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=int(access_token_expires.total_seconds())
    )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_token: str,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    تجديد access token باستخدام refresh token
    """
    payload = security_manager.verify_token(refresh_token)
    if not payload or payload.get("type") != "refresh":
        raise AuthenticationException("Refresh token غير صحيح")
    
    user_id = payload.get("sub")
    user = await user_crud.get(db, id=int(user_id))
    if not user or not user.is_active:
        raise AuthenticationException("المستخدم غير موجود أو غير نشط")
    
    # إنشاء access token جديد
    access_token = security_manager.create_access_token(subject=user.id)
    
    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.jwt_access_token_expire_minutes * 60
    )


@router.post("/logout", response_model=MessageResponse)
async def logout(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    تسجيل الخروج
    """
    # في التطبيق الحقيقي، يمكن إضافة token إلى blacklist
    return MessageResponse(message="تم تسجيل الخروج بنجاح")


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    الحصول على معلومات المستخدم الحالي
    """
    return UserResponse.from_orm(current_user)


@router.post("/forgot-password", response_model=MessageResponse)
async def forgot_password(
    request: ForgotPasswordRequest,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    طلب إعادة تعيين كلمة المرور
    """
    user = await user_crud.get_by_email(db, email=request.email)
    if user:
        # إنشاء token لإعادة التعيين
        reset_token = await user_crud.create_reset_token(db, user_id=user.id)
        
        # إرسال البريد الإلكتروني (يمكن تطبيقه لاحقاً)
        # await send_reset_password_email(user.email, reset_token)
    
    # نرجع نفس الرسالة سواء وُجد المستخدم أم لا (للأمان)
    return MessageResponse(
        message="إذا كان البريد الإلكتروني موجود، ستصلك رسالة لإعادة تعيين كلمة المرور"
    )


@router.post("/reset-password", response_model=MessageResponse)
async def reset_password(
    request: ResetPasswordRequest,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    إعادة تعيين كلمة المرور
    """
    user = await user_crud.verify_reset_token(db, token=request.token)
    if not user:
        raise ValidationException("رمز إعادة التعيين غير صحيح أو منتهي الصلاحية")
    
    # تحديث كلمة المرور
    await user_crud.update_password(db, user_id=user.id, new_password=request.new_password)
    
    return MessageResponse(message="تم تغيير كلمة المرور بنجاح")


@router.post("/verify-email", response_model=MessageResponse)
async def verify_email(
    token: str,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    تحقق البريد الإلكتروني
    """
    user = await user_crud.verify_email_token(db, token=token)
    if not user:
        raise ValidationException("رمز التحقق غير صحيح أو منتهي الصلاحية")
    
    return MessageResponse(message="تم تحقق البريد الإلكتروني بنجاح")


@router.post("/resend-verification", response_model=MessageResponse)
async def resend_verification(
    request: EmailVerificationRequest,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    إعادة إرسال رمز تحقق البريد الإلكتروني
    """
    user = await user_crud.get_by_email(db, email=request.email)
    if user and not user.is_verified:
        # إنشاء token جديد للتحقق
        verification_token = await user_crud.create_verification_token(db, user_id=user.id)
        
        # إرسال البريد الإلكتروني (يمكن تطبيقه لاحقاً)
        # await send_verification_email(user.email, verification_token)
    
    return MessageResponse(
        message="إذا كان البريد الإلكتروني غير محقق، ستصلك رسالة تحقق جديدة"
    )
