"""
نموذج العالم - Tamsik FastAPI
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, DECIMAL
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.database import Base


class Scholar(Base):
    """نموذج العالم"""
    
    __tablename__ = "scholars"
    
    # الحقول الأساسية
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    title = Column(String(200), nullable=True)
    bio = Column(Text, nullable=True)
    
    # المعلومات الأكاديمية
    specialization = Column(String(200), nullable=True)
    education = Column(Text, nullable=True)
    experience = Column(Text, nullable=True)
    
    # المعلومات الشخصية
    location = Column(String(100), nullable=True)
    image = Column(String(255), nullable=True)
    
    # معلومات الاتصال
    contact_info = Column(JSON, nullable=True)  # {"email": "", "phone": "", "website": ""}
    social_links = Column(JSON, nullable=True)  # {"twitter": "", "facebook": "", "youtube": ""}
    
    # الإعدادات
    is_featured = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # الإحصائيات
    fatwa_count = Column(Integer, default=0, nullable=False)
    rating = Column(DECIMAL(3, 2), default=0.00, nullable=False)
    
    # الربط بالمستخدم (اختياري)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # التواريخ
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # العلاقات
    user = relationship("User")
    fatwas = relationship("Fatwa", back_populates="scholar")
    
    def __repr__(self):
        return f"<Scholar(id={self.id}, name='{self.name}')>"
    
    @property
    def average_rating(self) -> float:
        """متوسط التقييم"""
        return float(self.rating) if self.rating else 0.0
    
    def update_fatwa_count(self):
        """تحديث عدد الفتاوى"""
        self.fatwa_count = len([f for f in self.fatwas if f.status == "published"])
    
    def calculate_rating(self):
        """حساب متوسط التقييم (يمكن تطويره لاحقاً)"""
        # يمكن إضافة نظام تقييم متقدم هنا
        pass
