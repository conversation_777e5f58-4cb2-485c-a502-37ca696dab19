"""
نظام الأمان والمصادقة - Tamsik FastAPI
"""
from datetime import datetime, timedelta
from typing import Optional, Union, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import settings
from app.database import get_db
from app.models.user import User
from app.crud.user import user_crud


# إعداد تشفير كلمات المرور
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# إعداد JWT Bearer
security = HTTPBearer()


class SecurityManager:
    """مدير الأمان والمصادقة"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """التحقق من كلمة المرور"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """تشفير كلمة المرور"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(
        subject: Union[str, Any], 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """إنشاء JWT access token"""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.jwt_access_token_expire_minutes
            )
        
        to_encode = {"exp": expire, "sub": str(subject), "type": "access"}
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.jwt_secret_key, 
            algorithm=settings.jwt_algorithm
        )
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(
        subject: Union[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """إنشاء JWT refresh token"""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                days=settings.jwt_refresh_token_expire_days
            )
        
        to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
        encoded_jwt = jwt.encode(
            to_encode,
            settings.jwt_secret_key,
            algorithm=settings.jwt_algorithm
        )
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Optional[dict]:
        """التحقق من صحة JWT token"""
        try:
            payload = jwt.decode(
                token,
                settings.jwt_secret_key,
                algorithms=[settings.jwt_algorithm]
            )
            return payload
        except JWTError:
            return None
    
    @staticmethod
    def validate_password(password: str) -> bool:
        """التحقق من قوة كلمة المرور"""
        if len(password) < settings.password_min_length:
            return False
        
        # يمكن إضافة المزيد من قواعد التحقق هنا
        # مثل: أحرف كبيرة، أرقام، رموز خاصة
        
        return True


# إنشاء instance من مدير الأمان
security_manager = SecurityManager()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    الحصول على المستخدم الحالي من JWT token
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="لا يمكن التحقق من بيانات الاعتماد",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # التحقق من وجود token
        if not credentials or not credentials.credentials:
            raise credentials_exception
        
        # فك تشفير token
        payload = security_manager.verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
        
        # التحقق من نوع token
        if payload.get("type") != "access":
            raise credentials_exception
        
        # الحصول على user_id
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        
        # البحث عن المستخدم في قاعدة البيانات
        user = await user_crud.get(db, id=int(user_id))
        if user is None:
            raise credentials_exception
        
        return user
        
    except (JWTError, ValueError):
        raise credentials_exception


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    الحصول على المستخدم النشط الحالي
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المستخدم غير نشط"
        )
    return current_user


async def get_current_verified_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    الحصول على المستخدم المحقق الحالي
    """
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="يجب تحقق البريد الإلكتروني أولاً"
        )
    return current_user


def require_roles(*allowed_roles: str):
    """
    Decorator للتحقق من صلاحيات المستخدم
    """
    def role_checker(current_user: User = Depends(get_current_verified_user)) -> User:
        if current_user.role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="ليس لديك صلاحية للوصول إلى هذا المورد"
            )
        return current_user
    
    return role_checker


# Shortcuts للأدوار المختلفة
require_admin = require_roles("admin")
require_scholar = require_roles("admin", "scholar")
require_member = require_roles("admin", "scholar", "member")


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    الحصول على المستخدم الحالي (اختياري)
    """
    if not credentials or not credentials.credentials:
        return None
    
    try:
        payload = security_manager.verify_token(credentials.credentials)
        if payload is None or payload.get("type") != "access":
            return None
        
        user_id = payload.get("sub")
        if user_id is None:
            return None
        
        user = await user_crud.get(db, id=int(user_id))
        return user if user and user.is_active else None
        
    except (JWTError, ValueError):
        return None


# تصدير المكونات المهمة
__all__ = [
    "SecurityManager",
    "security_manager",
    "get_current_user",
    "get_current_active_user", 
    "get_current_verified_user",
    "get_optional_user",
    "require_roles",
    "require_admin",
    "require_scholar",
    "require_member"
]
