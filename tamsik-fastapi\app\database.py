"""
إعداد قاعدة البيانات - Tamsik FastAPI
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from typing import AsyncGenerator

from app.config import settings


# إنشاء محرك قاعدة البيانات غير المتزامن
async_engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,
    future=True,
    pool_pre_ping=True,
    pool_recycle=300,
)

# إنشاء محرك قاعدة البيانات المتزامن (للمهام الخاصة)
sync_engine = create_engine(
    settings.database_url_sync,
    echo=settings.debug,
    pool_pre_ping=True,
    pool_recycle=300,
)

# إنشاء session factory
AsyncSessionLocal = sessionmaker(
    async_engine, 
    class_=AsyncSession, 
    expire_on_commit=False
)

# إنشاء Base للنماذج
Base = declarative_base()

# Metadata للجداول
metadata = MetaData()


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    الحصول على جلسة قاعدة بيانات غير متزامنة
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db():
    """
    تهيئة قاعدة البيانات
    """
    async with async_engine.begin() as conn:
        # إنشاء جميع الجداول
        await conn.run_sync(Base.metadata.create_all)


async def close_db():
    """
    إغلاق اتصالات قاعدة البيانات
    """
    await async_engine.dispose()


# دالة للحصول على قاعدة البيانات (dependency)
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency للحصول على جلسة قاعدة البيانات
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


# إعدادات الاتصال
class DatabaseConfig:
    """إعدادات قاعدة البيانات"""
    
    @staticmethod
    def get_async_engine():
        """الحصول على محرك قاعدة البيانات غير المتزامن"""
        return async_engine
    
    @staticmethod
    def get_sync_engine():
        """الحصول على محرك قاعدة البيانات المتزامن"""
        return sync_engine
    
    @staticmethod
    async def test_connection():
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            async with async_engine.begin() as conn:
                await conn.execute("SELECT 1")
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False


# تصدير المكونات المهمة
__all__ = [
    "Base",
    "async_engine",
    "sync_engine", 
    "AsyncSessionLocal",
    "get_db",
    "get_async_session",
    "init_db",
    "close_db",
    "DatabaseConfig",
    "metadata"
]
