version: '3.8'

services:
  # تطبيق FastAPI
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+aiomysql://tamsik_user:tamsik_password@db:3306/tamsik_db
      - DATABASE_URL_SYNC=mysql+pymysql://tamsik_user:tamsik_password@db:3306/tamsik_db
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
      - ENVIRONMENT=development
      - DEBUG=True
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # قاعدة بيانات MySQL
  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: tamsik_db
      MYSQL_USER: tamsik_user
      MYSQL_PASSWORD: tamsik_password
      MYSQL_ROOT_PASSWORD: root_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  # Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # phpMyAdmin لإدارة قاعدة البيانات (اختياري)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    environment:
      PMA_HOST: db
      PMA_USER: tamsik_user
      PMA_PASSWORD: tamsik_password
    ports:
      - "8080:80"
    depends_on:
      - db
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
