"""
API v1 Router - Tamsik FastAPI
"""
from fastapi import APIRouter

from app.api.v1 import auth, users, sermons, scholars, fatwas, lectures, categories

# إنشاء router رئيسي للـ API v1
api_router = APIRouter()

# إضافة جميع المسارات
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(sermons.router, prefix="/sermons", tags=["sermons"])
api_router.include_router(scholars.router, prefix="/scholars", tags=["scholars"])
api_router.include_router(fatwas.router, prefix="/fatwas", tags=["fatwas"])
api_router.include_router(lectures.router, prefix="/lectures", tags=["lectures"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
