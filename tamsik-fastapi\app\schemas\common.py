"""
Schemas مشتركة - Tamsik FastAPI
"""
from typing import Optional, List, Any, Dict
from pydantic import BaseModel, Field
from datetime import datetime


class BaseResponse(BaseModel):
    """استجابة أساسية"""
    success: bool = True
    message: str = "تم بنجاح"
    data: Optional[Any] = None


class ErrorResponse(BaseModel):
    """استجابة خطأ"""
    success: bool = False
    message: str
    details: Optional[Dict[str, Any]] = None
    error_code: Optional[str] = None


class PaginationParams(BaseModel):
    """معاملات الصفحات"""
    page: int = Field(1, ge=1, description="رقم الصفحة")
    limit: int = Field(10, ge=1, le=100, description="عدد العناصر في الصفحة")
    
    @property
    def offset(self) -> int:
        """حساب الإزاحة"""
        return (self.page - 1) * self.limit


class PaginatedResponse(BaseModel):
    """استجابة مع صفحات"""
    items: List[Any]
    total: int
    page: int
    limit: int
    pages: int
    has_next: bool
    has_prev: bool
    
    @classmethod
    def create(
        cls,
        items: List[Any],
        total: int,
        page: int,
        limit: int
    ):
        """إنشاء استجابة مع صفحات"""
        pages = (total + limit - 1) // limit
        return cls(
            items=items,
            total=total,
            page=page,
            limit=limit,
            pages=pages,
            has_next=page < pages,
            has_prev=page > 1
        )


class SearchParams(BaseModel):
    """معاملات البحث"""
    q: Optional[str] = Field(None, description="نص البحث")
    category_id: Optional[int] = Field(None, description="معرف التصنيف")
    tags: Optional[List[str]] = Field(None, description="الكلمات المفتاحية")
    date_from: Optional[datetime] = Field(None, description="من تاريخ")
    date_to: Optional[datetime] = Field(None, description="إلى تاريخ")
    sort_by: Optional[str] = Field("created_at", description="ترتيب حسب")
    sort_order: Optional[str] = Field("desc", regex="^(asc|desc)$", description="اتجاه الترتيب")


class FileUploadResponse(BaseModel):
    """استجابة رفع الملف"""
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    content_type: str
    upload_date: datetime


class StatsResponse(BaseModel):
    """استجابة الإحصائيات"""
    total_count: int
    active_count: Optional[int] = None
    featured_count: Optional[int] = None
    recent_count: Optional[int] = None
    additional_stats: Optional[Dict[str, Any]] = None


class HealthCheckResponse(BaseModel):
    """استجابة فحص الصحة"""
    status: str = "healthy"
    timestamp: datetime
    version: str
    environment: str
    database_status: Optional[str] = None
    redis_status: Optional[str] = None


class TokenResponse(BaseModel):
    """استجابة التوكن"""
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    expires_in: int


class MessageResponse(BaseModel):
    """استجابة رسالة بسيطة"""
    message: str
    
    class Config:
        schema_extra = {
            "example": {
                "message": "تم تنفيذ العملية بنجاح"
            }
        }
