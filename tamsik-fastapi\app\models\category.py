"""
نموذج التصنيف - Tamsik FastAPI
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.database import Base


class CategoryType(str, enum.Enum):
    """أنواع التصنيفات"""
    SERMON = "sermon"
    FATWA = "fatwa"
    LECTURE = "lecture"
    GENERAL = "general"


class Category(Base):
    """نموذج التصنيف"""
    
    __tablename__ = "categories"
    
    # الحقول الأساسية
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    slug = Column(String(100), unique=True, index=True, nullable=False)
    
    # النوع والهيكل الهرمي
    type = Column(Enum(CategoryType), default=CategoryType.GENERAL, nullable=False)
    parent_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    
    # الإعدادات
    is_active = Column(Boolean, default=True, nullable=False)
    sort_order = Column(Integer, default=0, nullable=False)
    
    # معلومات إضافية
    icon = Column(String(100), nullable=True)
    color = Column(String(7), nullable=True)  # HEX color code
    image = Column(String(255), nullable=True)
    
    # الإحصائيات
    items_count = Column(Integer, default=0, nullable=False)
    
    # التواريخ
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # العلاقات
    parent = relationship("Category", remote_side=[id], back_populates="children")
    children = relationship("Category", back_populates="parent", cascade="all, delete-orphan")
    sermons = relationship("Sermon", back_populates="category")
    fatwas = relationship("Fatwa", back_populates="category")
    lectures = relationship("Lecture", back_populates="category")
    
    def __repr__(self):
        return f"<Category(id={self.id}, name='{self.name}', type='{self.type}')>"
    
    @property
    def full_path(self) -> str:
        """المسار الكامل للتصنيف"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name
    
    @property
    def level(self) -> int:
        """مستوى التصنيف في الهيكل الهرمي"""
        if self.parent:
            return self.parent.level + 1
        return 0
    
    @property
    def is_root(self) -> bool:
        """التحقق من كون التصنيف جذري"""
        return self.parent_id is None
    
    @property
    def has_children(self) -> bool:
        """التحقق من وجود تصنيفات فرعية"""
        return len(self.children) > 0
    
    def get_all_children(self) -> list:
        """الحصول على جميع التصنيفات الفرعية (بشكل تكراري)"""
        all_children = []
        for child in self.children:
            all_children.append(child)
            all_children.extend(child.get_all_children())
        return all_children
    
    def update_items_count(self):
        """تحديث عدد العناصر في التصنيف"""
        count = 0
        
        if self.type == CategoryType.SERMON:
            count = len([s for s in self.sermons if s.is_published])
        elif self.type == CategoryType.FATWA:
            count = len([f for f in self.fatwas if f.status == "published"])
        elif self.type == CategoryType.LECTURE:
            count = len([l for l in self.lectures if l.is_active])
        
        # إضافة عدد العناصر من التصنيفات الفرعية
        for child in self.children:
            count += child.items_count
        
        self.items_count = count
