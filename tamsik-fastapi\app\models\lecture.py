"""
نموذج المحاضرة - Tamsik FastAPI
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Time, Date, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.database import Base


class LectureType(str, enum.Enum):
    """أنواع المحاضرات"""
    LECTURE = "محاضرة"
    LESSON = "درس"
    SEMINAR = "ندوة"
    COURSE = "دورة"


class DayOfWeek(str, enum.Enum):
    """أيام الأسبوع"""
    SATURDAY = "السبت"
    SUNDAY = "الأحد"
    MONDAY = "الاثنين"
    TUESDAY = "الثلاثاء"
    WEDNESDAY = "الأربعاء"
    THURSDAY = "الخميس"
    FRIDAY = "الجمعة"


class Lecture(Base):
    """نموذج المحاضرة"""
    
    __tablename__ = "lectures"
    
    # الحقول الأساسية
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # المحاضر
    lecturer_name = Column(String(255), nullable=False)
    lecturer_bio = Column(Text, nullable=True)
    
    # النوع والتصنيف
    type = Column(Enum(LectureType), default=LectureType.LECTURE, nullable=False)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    
    # المكان والزمان
    province = Column(String(100), nullable=False, index=True)
    city = Column(String(100), nullable=True)
    location = Column(String(255), nullable=False)
    
    # التوقيت
    day_of_week = Column(Enum(DayOfWeek), nullable=False)
    time = Column(Time, nullable=False)
    
    # التواريخ
    start_date = Column(Date, nullable=True)
    end_date = Column(Date, nullable=True)
    
    # الإعدادات
    is_active = Column(Boolean, default=True, nullable=False)
    is_recurring = Column(Boolean, default=True, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    
    # معلومات الاتصال
    contact_info = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    email = Column(String(100), nullable=True)
    
    # معلومات إضافية
    capacity = Column(Integer, nullable=True)
    registration_required = Column(Boolean, default=False, nullable=False)
    fee = Column(String(50), nullable=True)  # "مجاني" أو مبلغ
    
    # المنشئ
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # التواريخ
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # العلاقات
    category = relationship("Category", back_populates="lectures")
    creator = relationship("User")
    
    def __repr__(self):
        return f"<Lecture(id={self.id}, title='{self.title}', province='{self.province}')>"
    
    @property
    def is_ongoing(self) -> bool:
        """التحقق من كون المحاضرة جارية"""
        if not self.is_active:
            return False
        
        if self.is_recurring:
            return True
        
        from datetime import date
        today = date.today()
        
        if self.start_date and self.end_date:
            return self.start_date <= today <= self.end_date
        elif self.start_date:
            return today >= self.start_date
        
        return True
    
    @property
    def full_location(self) -> str:
        """الموقع الكامل"""
        if self.city:
            return f"{self.location}, {self.city}, {self.province}"
        return f"{self.location}, {self.province}"
    
    @property
    def schedule_info(self) -> str:
        """معلومات الجدولة"""
        time_str = self.time.strftime("%H:%M") if self.time else ""
        return f"{self.day_of_week} - {time_str}"
