{"name": "tamsik-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "prod": "NODE_ENV=production node server.js", "serve": "node -e \"const express = require('express'); const app = express(); app.use(express.static('public')); app.use(express.static('.')); app.listen(3000, () => console.log('Server running on http://localhost:3000'));\"", "setup-db": "node scripts/setupDatabase.js", "init-db": "node config/initDatabase.js", "test-db": "node -e \"require('./config/database').testConnection()\"", "test": "echo \"Error: no test specified\" && exit 1", "build": "echo \"No build step required for Node.js\"", "deploy": "node scripts/deploy.js", "backup": "node scripts/backup.js", "test-api": "node scripts/testAPI.js", "postinstall": "node config/sqlite-setup.js && node scripts/setupDatabase.js"}, "keywords": ["islamic", "sermons", "scholars", "yemen", "arabic", "religious", "nodejs", "express"], "author": "Tamsik Team", "license": "ISC", "description": "Backend API for Tamsik Islamic platform", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.0.2"}}