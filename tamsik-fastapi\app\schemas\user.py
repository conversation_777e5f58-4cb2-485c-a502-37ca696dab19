"""
User Schemas - Tamsik FastAPI
"""
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, validator
from datetime import datetime

from app.models.user import UserRole


class UserBase(BaseModel):
    """Base user schema"""
    username: str = Field(..., min_length=3, max_length=50, description="اسم المستخدم")
    email: EmailStr = Field(..., description="البريد الإلكتروني")
    full_name: str = Field(..., min_length=2, max_length=100, description="الاسم الكامل")
    phone: Optional[str] = Field(None, max_length=20, description="رقم الهاتف")
    location: Optional[str] = Field(None, max_length=100, description="الموقع")
    bio: Optional[str] = Field(None, max_length=1000, description="نبذة شخصية")


class UserCreate(UserBase):
    """Schema لإنشاء مستخدم جديد"""
    password: str = Field(..., min_length=8, description="كلمة المرور")
    confirm_password: str = Field(..., description="تأكيد كلمة المرور")
    role: Optional[UserRole] = Field(UserRole.USER, description="دور المستخدم")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('كلمات المرور غير متطابقة')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
        return v


class UserUpdate(BaseModel):
    """Schema لتحديث المستخدم"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, min_length=2, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    location: Optional[str] = Field(None, max_length=100)
    bio: Optional[str] = Field(None, max_length=1000)
    profile_image: Optional[str] = None


class UserPasswordChange(BaseModel):
    """Schema لتغيير كلمة المرور"""
    current_password: str = Field(..., description="كلمة المرور الحالية")
    new_password: str = Field(..., min_length=8, description="كلمة المرور الجديدة")
    confirm_new_password: str = Field(..., description="تأكيد كلمة المرور الجديدة")
    
    @validator('confirm_new_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('كلمات المرور غير متطابقة')
        return v


class UserLogin(BaseModel):
    """Schema لتسجيل الدخول"""
    email: EmailStr = Field(..., description="البريد الإلكتروني")
    password: str = Field(..., description="كلمة المرور")
    remember_me: Optional[bool] = Field(False, description="تذكرني")


class UserResponse(BaseModel):
    """Schema لاستجابة المستخدم"""
    id: int
    username: str
    email: str
    full_name: str
    role: UserRole
    is_active: bool
    is_verified: bool
    phone: Optional[str] = None
    location: Optional[str] = None
    bio: Optional[str] = None
    profile_image: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserPublicResponse(BaseModel):
    """Schema للمعلومات العامة للمستخدم"""
    id: int
    username: str
    full_name: str
    role: UserRole
    bio: Optional[str] = None
    profile_image: Optional[str] = None
    location: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """Schema لقائمة المستخدمين"""
    id: int
    username: str
    email: str
    full_name: str
    role: UserRole
    is_active: bool
    is_verified: bool
    created_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserStatsResponse(BaseModel):
    """Schema لإحصائيات المستخدم"""
    sermons_count: int = 0
    fatwas_count: int = 0
    total_views: int = 0
    total_likes: int = 0


class ForgotPasswordRequest(BaseModel):
    """Schema لطلب نسيان كلمة المرور"""
    email: EmailStr = Field(..., description="البريد الإلكتروني")


class ResetPasswordRequest(BaseModel):
    """Schema لإعادة تعيين كلمة المرور"""
    token: str = Field(..., description="رمز إعادة التعيين")
    new_password: str = Field(..., min_length=8, description="كلمة المرور الجديدة")
    confirm_password: str = Field(..., description="تأكيد كلمة المرور")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('كلمات المرور غير متطابقة')
        return v


class EmailVerificationRequest(BaseModel):
    """Schema لطلب تحقق البريد الإلكتروني"""
    email: EmailStr = Field(..., description="البريد الإلكتروني")


# تصدير جميع الـ schemas
__all__ = [
    "UserBase",
    "UserCreate", 
    "UserUpdate",
    "UserPasswordChange",
    "UserLogin",
    "UserResponse",
    "UserPublicResponse",
    "UserListResponse",
    "UserStatsResponse",
    "ForgotPasswordRequest",
    "ResetPasswordRequest",
    "EmailVerificationRequest"
]
