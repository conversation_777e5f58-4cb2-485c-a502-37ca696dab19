"""
نموذج المستخدم - Tamsik FastAPI
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.database import Base


class UserRole(str, enum.Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"
    SCHOLAR = "scholar"
    MEMBER = "member"
    USER = "user"


class User(Base):
    """نموذج المستخدم"""
    
    __tablename__ = "users"
    
    # الحقول الأساسية
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    
    # الدور والصلاحيات
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # معلومات التحقق
    verification_token = Column(String(255), nullable=True)
    reset_password_token = Column(String(255), nullable=True)
    reset_password_expires = Column(DateTime, nullable=True)
    
    # معلومات شخصية
    profile_image = Column(String(255), nullable=True)
    bio = Column(Text, nullable=True)
    phone = Column(String(20), nullable=True)
    location = Column(String(100), nullable=True)
    
    # التواريخ
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # العلاقات
    sermons = relationship("Sermon", back_populates="author", cascade="all, delete-orphan")
    fatwas = relationship("Fatwa", back_populates="questioner", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @property
    def is_admin(self) -> bool:
        """التحقق من كون المستخدم مدير"""
        return self.role == UserRole.ADMIN or self.is_superuser
    
    @property
    def is_scholar(self) -> bool:
        """التحقق من كون المستخدم عالم"""
        return self.role in [UserRole.ADMIN, UserRole.SCHOLAR] or self.is_superuser
    
    @property
    def can_create_content(self) -> bool:
        """التحقق من إمكانية إنشاء المحتوى"""
        return self.role in [UserRole.ADMIN, UserRole.SCHOLAR, UserRole.MEMBER] or self.is_superuser
    
    def has_permission(self, permission: str) -> bool:
        """التحقق من وجود صلاحية معينة"""
        permissions = {
            UserRole.ADMIN: ["read", "write", "delete", "manage_users", "manage_content"],
            UserRole.SCHOLAR: ["read", "write", "manage_fatwas"],
            UserRole.MEMBER: ["read", "write"],
            UserRole.USER: ["read"]
        }
        
        if self.is_superuser:
            return True
        
        user_permissions = permissions.get(self.role, [])
        return permission in user_permissions
