"""
إعدادات التطبيق - Tamsik FastAPI
"""
from typing import List, Optional
from pydantic import BaseSettings, validator
from decouple import config


class Settings(BaseSettings):
    """إعدادات التطبيق الرئيسية"""
    
    # معلومات التطبيق
    app_name: str = config("APP_NAME", default="Tamsik Islamic Platform")
    app_version: str = config("APP_VERSION", default="2.0.0")
    debug: bool = config("DEBUG", default=False, cast=bool)
    environment: str = config("ENVIRONMENT", default="development")
    
    # قاعدة البيانات
    database_url: str = config("DATABASE_URL")
    database_url_sync: str = config("DATABASE_URL_SYNC")
    
    # JWT
    jwt_secret_key: str = config("JWT_SECRET_KEY")
    jwt_algorithm: str = config("JWT_ALGORITHM", default="HS256")
    jwt_access_token_expire_minutes: int = config("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", default=30, cast=int)
    jwt_refresh_token_expire_days: int = config("JWT_REFRESH_TOKEN_EXPIRE_DAYS", default=7, cast=int)
    
    # CORS
    allowed_origins: List[str] = config(
        "ALLOWED_ORIGINS", 
        default="http://localhost:3000,http://localhost:8000",
        cast=lambda v: [s.strip() for s in v.split(',')]
    )
    
    # Redis
    redis_url: str = config("REDIS_URL", default="redis://localhost:6379/0")
    
    # البريد الإلكتروني
    mail_username: str = config("MAIL_USERNAME", default="")
    mail_password: str = config("MAIL_PASSWORD", default="")
    mail_from: str = config("MAIL_FROM", default="")
    mail_port: int = config("MAIL_PORT", default=587, cast=int)
    mail_server: str = config("MAIL_SERVER", default="smtp.gmail.com")
    mail_from_name: str = config("MAIL_FROM_NAME", default="Tamsik Platform")
    
    # رفع الملفات
    max_file_size: int = config("MAX_FILE_SIZE", default=10485760, cast=int)  # 10MB
    upload_dir: str = config("UPLOAD_DIR", default="uploads/")
    allowed_extensions: List[str] = config(
        "ALLOWED_EXTENSIONS",
        default="jpg,jpeg,png,pdf,doc,docx",
        cast=lambda v: [s.strip() for s in v.split(',')]
    )
    
    # الصفحات
    default_page_size: int = config("DEFAULT_PAGE_SIZE", default=10, cast=int)
    max_page_size: int = config("MAX_PAGE_SIZE", default=100, cast=int)
    
    # الأمان
    bcrypt_rounds: int = config("BCRYPT_ROUNDS", default=12, cast=int)
    password_min_length: int = config("PASSWORD_MIN_LENGTH", default=8, cast=int)
    rate_limit_per_minute: int = config("RATE_LIMIT_PER_MINUTE", default=60, cast=int)
    
    # السجلات
    log_level: str = config("LOG_LEVEL", default="INFO")
    log_file: str = config("LOG_FILE", default="logs/tamsik.log")
    
    # النصوص العربية
    default_language: str = config("DEFAULT_LANGUAGE", default="ar")
    support_rtl: bool = config("SUPPORT_RTL", default=True, cast=bool)
    
    @validator("jwt_secret_key")
    def validate_jwt_secret(cls, v):
        if len(v) < 32:
            raise ValueError("JWT secret key must be at least 32 characters long")
        return v
    
    @validator("database_url")
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("Database URL is required")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# إنشاء instance من الإعدادات
settings = Settings()


# إعدادات قاعدة البيانات
class DatabaseSettings:
    """إعدادات قاعدة البيانات"""
    
    @staticmethod
    def get_database_url() -> str:
        """الحصول على رابط قاعدة البيانات"""
        return settings.database_url
    
    @staticmethod
    def get_sync_database_url() -> str:
        """الحصول على رابط قاعدة البيانات المتزامن"""
        return settings.database_url_sync


# إعدادات الأمان
class SecuritySettings:
    """إعدادات الأمان"""
    
    @staticmethod
    def get_password_hash_settings() -> dict:
        """إعدادات تشفير كلمات المرور"""
        return {
            "schemes": ["bcrypt"],
            "deprecated": "auto",
            "bcrypt__rounds": settings.bcrypt_rounds
        }


# إعدادات البريد الإلكتروني
class EmailSettings:
    """إعدادات البريد الإلكتروني"""
    
    @staticmethod
    def get_email_config() -> dict:
        """إعدادات البريد الإلكتروني"""
        return {
            "MAIL_USERNAME": settings.mail_username,
            "MAIL_PASSWORD": settings.mail_password,
            "MAIL_FROM": settings.mail_from,
            "MAIL_PORT": settings.mail_port,
            "MAIL_SERVER": settings.mail_server,
            "MAIL_FROM_NAME": settings.mail_from_name,
            "MAIL_STARTTLS": True,
            "MAIL_SSL_TLS": False,
            "USE_CREDENTIALS": True,
            "VALIDATE_CERTS": True
        }


# تصدير الإعدادات
__all__ = [
    "settings",
    "DatabaseSettings", 
    "SecuritySettings",
    "EmailSettings"
]
